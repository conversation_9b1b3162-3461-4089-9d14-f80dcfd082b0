#!/usr/bin/env python3

import os
import asyncio
import logging
from aiohttp_client_cache.session import CachedSession
from aiohttp_client_cache.backends.filesystem import FileBackend
from input_data import get_go_bots_api_response

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)

logger = logging.getLogger()

async def main():
    logger.info("Starting simplified script...")
    
    # Read merchants
    try:
        with open('merchants.txt', 'r') as f:
            merchants_ids = [uid.strip() for uid in f.read().split(',')]
        logger.info(f"Read {len(merchants_ids)} merchant IDs: {merchants_ids}")
    except Exception as e:
        logger.error(f"Failed to read merchants.txt: {e}")
        return
    
    # Set up cache
    cache_dir = os.path.join(os.getcwd(), 'cache')
    os.makedirs(cache_dir, exist_ok=True)
    
    cache = FileBackend(
        cache_name=cache_dir,
        expire_after=3600,
    )
    
    async with CachedSession(cache=cache) as session:
        logger.info("Getting GoBots data...")
        go_bots_data = await get_go_bots_api_response(session)
        
        if not go_bots_data:
            logger.error("Failed to fetch GoBots data")
            return
        
        logger.info(f"Got {len(go_bots_data)} items from GoBots API")
        
        # Find matches for our merchants
        for merchant_id in merchants_ids:
            logger.info(f"Looking for merchant: {merchant_id}")
            
            matches = []
            for item in go_bots_data:
                if item.get('merchant') and item.get('merchant', {}).get('id') == merchant_id:
                    matches.append({
                        "access_token": item['access_token'],
                        "seller_id": item['access_token'].split("-")[-1]
                    })
            
            logger.info(f"Found {len(matches)} matches for merchant {merchant_id}")
            for match in matches:
                logger.info(f"  - Seller ID: {match['seller_id']}")

if __name__ == "__main__":
    asyncio.run(main())
