#!/usr/bin/env python3

import os
import asyncio
import logging
from aiohttp_client_cache.session import CachedSession
from aiohttp_client_cache.backends.filesystem import FileBackend

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)

logger = logging.getLogger()

async def test_gobots_api():
    """Test if we can call the GoBots API"""
    try:
        from input_data import get_go_bots_api_response
        
        # Set up cache directory
        cache_dir = os.path.join(os.getcwd(), 'cache')
        os.makedirs(cache_dir, exist_ok=True)
        
        # Create a file cache
        cache = FileBackend(
            cache_name=cache_dir,
            expire_after=3600,
        )
        
        async with CachedSession(cache=cache) as session:
            logger.info("Testing GoBots API call...")
            go_bots_data = await get_go_bots_api_response(session)
            
            if go_bots_data:
                logger.info(f"✓ GoBots API successful! Got {len(go_bots_data)} items")
                return True
            else:
                logger.error("✗ GoBots API returned None")
                return False
                
    except Exception as e:
        logger.error(f"✗ GoBots API failed: {e}")
        return False

async def main():
    logger.info("Starting debug test...")
    
    # Check if GOBOTS_TOKEN is set
    token = os.environ.get('GOBOTS_TOKEN')
    if token:
        logger.info("✓ GOBOTS_TOKEN environment variable is set")
    else:
        logger.warning("✗ GOBOTS_TOKEN environment variable is not set")
    
    # Test GoBots API
    success = await test_gobots_api()
    
    if success:
        logger.info("✓ All tests passed!")
    else:
        logger.error("✗ Some tests failed")

if __name__ == "__main__":
    asyncio.run(main())
