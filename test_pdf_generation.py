#!/usr/bin/env python3
"""
Test script to debug PDF generation issues.
"""
import asyncio
import os
import sys
from pdf_helper import convert_html_to_pdf

async def test_pdf_generation():
    """Test PDF generation with a simple HTML content."""
    print("🧪 Testing PDF Generation...")
    
    # Simple test HTML content
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Test PDF</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #4a90e2; }
            .test-content { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        </style>
    </head>
    <body>
        <h1>PDF Generation Test</h1>
        <div class="test-content">
            <p>This is a test to verify that PDF generation is working correctly.</p>
            <p>If you can see this content in a PDF file, then the PDF generation is working!</p>
            <ul>
                <li>HTML to PDF conversion: ✅</li>
                <li>CSS styling: ✅</li>
                <li>UTF-8 encoding: ✅</li>
            </ul>
        </div>
    </body>
    </html>
    """
    
    # Create test output directory
    test_dir = "test_output"
    os.makedirs(test_dir, exist_ok=True)
    
    # Test PDF path
    test_pdf_path = os.path.join(test_dir, "test_pdf_generation.pdf")
    
    print(f"📄 Generating test PDF at: {test_pdf_path}")
    
    try:
        # Test PDF generation
        success = await convert_html_to_pdf(test_html, test_pdf_path)
        
        if success:
            if os.path.exists(test_pdf_path):
                file_size = os.path.getsize(test_pdf_path)
                print(f"✅ PDF generation successful!")
                print(f"📊 File size: {file_size:,} bytes")
                print(f"📁 Location: {os.path.abspath(test_pdf_path)}")
                return True
            else:
                print(f"❌ PDF generation reported success but file not found")
                return False
        else:
            print(f"❌ PDF generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during PDF generation test: {e}")
        return False

async def test_ghostscript_availability():
    """Test if Ghostscript is available."""
    print("\n🔍 Testing Ghostscript availability...")
    
    import subprocess
    
    try:
        # Try to run gswin64c
        result = subprocess.run(['gswin64c', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Ghostscript is available: {result.stdout.strip()}")
            return True
        else:
            print(f"⚠️ Ghostscript command failed: {result.stderr}")
            return False
    except FileNotFoundError:
        print(f"❌ Ghostscript (gswin64c) not found in PATH")
        print(f"💡 PDF generation will work but files will be larger (uncompressed)")
        return False
    except subprocess.TimeoutExpired:
        print(f"⏰ Ghostscript command timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing Ghostscript: {e}")
        return False

async def test_playwright_availability():
    """Test if Playwright is available and working."""
    print("\n🎭 Testing Playwright availability...")
    
    try:
        from playwright.async_api import async_playwright
        
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            await page.set_content("<html><body><h1>Test</h1></body></html>")
            await browser.close()
            
        print(f"✅ Playwright is working correctly")
        return True
        
    except ImportError:
        print(f"❌ Playwright not installed")
        print(f"💡 Install with: pip install playwright")
        print(f"💡 Then run: playwright install chromium")
        return False
    except Exception as e:
        print(f"❌ Playwright error: {e}")
        print(f"💡 Try running: playwright install chromium")
        return False

async def main():
    """Run all PDF generation tests."""
    print("🚀 PDF Generation Diagnostic Tests")
    print("=" * 50)
    
    tests = [
        ("Playwright", test_playwright_availability),
        ("Ghostscript", test_ghostscript_availability),
        ("PDF Generation", test_pdf_generation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    print(f"\n📊 Test Results Summary:")
    print("=" * 30)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:15} {status}")
        if not passed:
            all_passed = False
    
    print(f"\n🎯 Overall Status: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if not all_passed:
        print(f"\n💡 Troubleshooting Tips:")
        if not results.get("Playwright", False):
            print(f"   • Install Playwright: pip install playwright")
            print(f"   • Install browser: playwright install chromium")
        if not results.get("Ghostscript", False):
            print(f"   • Ghostscript is optional but recommended for smaller PDF files")
            print(f"   • Download from: https://www.ghostscript.com/download/gsdnld.html")
        if not results.get("PDF Generation", False):
            print(f"   • Check the logs above for specific error messages")
            print(f"   • Ensure you have write permissions to the output directory")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
