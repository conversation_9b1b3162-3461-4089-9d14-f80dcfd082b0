#!/usr/bin/env python3

print("Testing imports...")

try:
    from input_data import get_go_bots_api_response
    print("✓ input_data import successful")
except Exception as e:
    print(f"✗ input_data import failed: {e}")

try:
    from recommendation_report import filter_input
    print("✓ recommendation_report import successful")
except Exception as e:
    print(f"✗ recommendation_report import failed: {e}")

try:
    from pdf_helper import convert_html_to_pdf
    print("✓ pdf_helper import successful")
except Exception as e:
    print(f"✗ pdf_helper import failed: {e}")

try:
    from services.mercadolibre_service import MercadoLibreClient
    print("✓ services.mercadolibre_service import successful")
except Exception as e:
    print(f"✗ services.mercadolibre_service import failed: {e}")

try:
    from aiohttp_client_cache.session import CachedSession
    from aiohttp_client_cache.backends.filesystem import FileBackend
    print("✓ aiohttp_client_cache imports successful")
except Exception as e:
    print(f"✗ aiohttp_client_cache imports failed: {e}")

print("All import tests completed!")
